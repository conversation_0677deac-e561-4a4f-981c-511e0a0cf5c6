#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
扩展测试AlphaStrategy策略 - 多天回测
"""

import sys
from datetime import datetime
import pandas as pd
import numpy as np

# 添加VeighNa路径
sys.path.insert(0, r'c:\veighna_studio\Lib\site-packages')

from vnpy_ctastrategy.backtesting import BacktestingEngine
from vnpy_ctastrategy.strategies.MyStrategy import AlphaStrategy
from vnpy_ctastrategy.base import BacktestingMode
from vnpy.trader.constant import Interval, Exchange
from vnpy.trader.object import BarData

def create_extended_test_data():
    """创建扩展的测试用K线数据"""
    print("创建扩展测试数据...")
    
    # 生成200天的测试数据
    dates = pd.date_range(start='2023-01-01', periods=200, freq='D')
    
    # 模拟价格数据 - 创建一个有趋势的价格序列
    np.random.seed(42)
    base_price = 100.0
    
    # 创建一个有趋势变化的价格序列
    trend_changes = np.random.normal(0, 0.01, 200)  # 基础随机变化
    # 添加一些周期性变化
    for i in range(len(trend_changes)):
        if i % 20 == 0:  # 每20天添加一个较大的变化
            trend_changes[i] += np.random.choice([-0.05, 0.05])  # 5%的跳跃
    
    prices = [base_price]
    for change in trend_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 10.0))  # 确保价格不会太低
    
    bars = []
    for i, date in enumerate(dates):
        # 生成OHLC数据
        close = prices[i]
        daily_volatility = abs(np.random.normal(0, 0.02))  # 日内波动
        high = close * (1 + daily_volatility)
        low = close * (1 - daily_volatility)
        open_price = close * (1 + np.random.normal(0, 0.01))
        volume = np.random.randint(5000, 20000)
        
        bar = BarData(
            symbol="TEST",
            exchange=Exchange.SSE,
            datetime=datetime.combine(date.date(), datetime.min.time()),
            interval=Interval.DAILY,
            volume=volume,
            turnover=close * volume,
            open_price=open_price,
            high_price=high,
            low_price=low,
            close_price=close,
            gateway_name="TEST"
        )
        bars.append(bar)
    
    print(f"生成了{len(bars)}条扩展测试数据")
    print(f"价格范围: {min(prices):.2f} - {max(prices):.2f}")
    return bars

def test_extended_strategy():
    """扩展策略测试"""
    print("开始扩展测试AlphaStrategy...")
    
    # 创建回测引擎
    engine = BacktestingEngine()
    
    # 设置回测参数 - 更长的时间范围
    engine.set_parameters(
        vt_symbol="TEST.SSE",
        interval=Interval.DAILY,
        start=datetime(2023, 1, 1),
        end=datetime(2023, 7, 20),  # 扩展到7月
        rate=0.0002,
        slippage=0.001,
        size=1,
        pricetick=0.01,
        capital=100000,
        mode=BacktestingMode.BAR
    )
    
    # 策略参数 - 更敏感的设置
    strategy_setting = {
        "alpha_factors": ["alpha001", "alpha015", "alpha030"],
        "factor_weights": [0.33, 0.33, 0.34],
        "signal_threshold": 0.03,  # 进一步降低阈值
        "lookback_days": 15,  # 减少回看天数
        "debug_mode": True,
        "debug_interval": 20,  # 减少调试输出频率
        "risk_percent": 0.05,  # 增加风险比例
        "max_position": 10
    }
    
    # 添加策略
    engine.add_strategy(AlphaStrategy, strategy_setting)
    
    # 创建并加载测试数据
    test_bars = create_extended_test_data()
    engine.history_data = test_bars
    
    print("开始扩展回测...")
    try:
        # 运行回测
        engine.run_backtesting()
        
        # 计算结果
        df = engine.calculate_result()
        
        # 输出结果
        print("\n" + "="*60)
        print("扩展回测结果:")
        print("="*60)
        
        if engine.trades:
            print(f"总交易次数: {len(engine.trades)}")
            print("\n前10笔交易:")
            for i, (trade_id, trade) in enumerate(list(engine.trades.items())[:10]):
                direction = "买入" if trade.direction.value == "多" else "卖出"
                print(f"  {i+1}. {trade.datetime.strftime('%Y-%m-%d')} {direction} {trade.volume}手 @ {trade.price:.2f}")
            
            if len(engine.trades) > 10:
                print(f"  ... 还有{len(engine.trades)-10}笔交易")
        else:
            print("没有产生交易记录")
        
        # 输出订单信息
        if engine.limit_orders:
            print(f"\n限价单数量: {len(engine.limit_orders)}")
            active_orders = len(engine.active_limit_orders)
            print(f"活跃订单数量: {active_orders}")
        
        print(f"\n策略关键日志:")
        # 筛选包含交易信号的日志
        signal_logs = [log for log in engine.logs if any(keyword in log for keyword in ['信号:', '开多仓', '开空仓', '平仓'])]
        for log in signal_logs[-20:]:  # 显示最后20条相关日志
            print(f"  {log}")
            
        if df is not None and not df.empty:
            print(f"\n回测统计:")
            print(f"DataFrame列: {list(df.columns)}")
            if 'net_pnl' in df.columns:
                final_pnl = df['net_pnl'].iloc[-1]
                print(f"最终盈亏: {final_pnl:.2f}")
            if 'total_return' in df.columns:
                print(f"总收益率: {df['total_return'].iloc[-1]:.2%}")
        
    except Exception as e:
        print(f"回测过程中出现错误: {e}")
        
        # 输出详细的策略日志
        print(f"\n详细策略日志:")
        for log in engine.logs[-30:]:  # 显示最后30条日志
            print(f"  {log}")

if __name__ == "__main__":
    test_extended_strategy()
