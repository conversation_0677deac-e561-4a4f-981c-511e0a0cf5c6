#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证MyStrategy.py文件是否已经清理完成
"""

def verify_cleanup():
    """验证代码清理情况"""
    file_path = r"Lib/site-packages/vnpy_ctastrategy/strategies/MyStrategy.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("🔍 检查MyStrategy.py清理情况...")
        print("=" * 60)
        
        # 检查调试相关代码
        debug_items = [
            ("debug_mode", "调试模式参数"),
            ("debug_interval", "调试间隔参数"),
            ("force_trade_test", "强制交易测试参数"),
            ("bar_count", "K线计数器变量"),
            ("debug_info", "调试信息存储"),
            ("last_debug_time", "上次调试时间"),
            ("_debug_strategy_status", "调试状态方法"),
            ("_check_data_availability", "数据可用性检查方法"),
        ]
        
        found_debug_code = []
        for item, desc in debug_items:
            if item in content:
                found_debug_code.append(f"  ❌ 发现 {desc}: {item}")
            else:
                print(f"  ✅ 已删除 {desc}")
        
        if found_debug_code:
            print("\n⚠️  仍存在的调试代码:")
            for item in found_debug_code:
                print(item)
        else:
            print("\n🎉 所有调试代码已成功删除!")
        
        # 检查文件行数
        lines = content.split('\n')
        print(f"\n📊 文件统计:")
        print(f"  总行数: {len(lines)}")
        
        # 检查关键方法是否存在
        key_methods = [
            "def on_init",
            "def on_start", 
            "def on_bar",
            "def _calculate_alpha_factors",
            "def _execute_trading_strategy"
        ]
        
        print(f"\n🔧 核心方法检查:")
        for method in key_methods:
            if method in content:
                print(f"  ✅ {method}")
            else:
                print(f"  ❌ {method} - 缺失!")
        
        # 检查是否还有过多的调试日志
        debug_logs = content.count('write_log')
        print(f"\n📝 日志输出统计:")
        print(f"  write_log调用次数: {debug_logs}")
        if debug_logs > 10:
            print("  ⚠️  可能仍有过多的调试日志")
        else:
            print("  ✅ 日志输出已适度清理")
        
        return len(found_debug_code) == 0
        
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        return False

if __name__ == "__main__":
    success = verify_cleanup()
    print("\n" + "=" * 60)
    if success:
        print("✅ 代码清理验证通过!")
    else:
        print("❌ 代码清理不完整，需要进一步处理")
