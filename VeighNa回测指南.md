# VeighNa平台AlphaStrategy回测指南

## 🎯 策略已验证可用！

根据测试结果，AlphaStrategy策略已经能够：
- ✅ 正确计算Alpha101因子
- ✅ 生成交易信号（综合Alpha: -0.301549, 信号: -1）
- ✅ 执行交易逻辑（发出卖空信号）

## 📋 在VeighNa平台上配置回测

### 1. 策略参数配置

在VeighNa的CTA策略回测界面中，使用以下参数：

```json
{
    "alpha_factors": ["alpha001", "alpha015", "alpha030"],
    "factor_weights": [0.33, 0.33, 0.34],
    "signal_threshold": 0.01,
    "lookback_days": 15,
    "risk_percent": 0.02,
    "atr_period": 10,
    "atr_multiplier": 2.0,
    "price_offset": 0.002,
    "max_position": 1,
    "debug_mode": true
}
```

### 2. 回测设置

**基本设置：**
- 策略类名：`AlphaStrategy`
- 合约代码：`000001.SZSE`（平安银行）或其他活跃股票
- 开始日期：`2023-01-01`
- 结束日期：`2023-12-31`
- K线周期：`日线`

**资金设置：**
- 初始资金：`100000`
- 手续费率：`0.0003`（万分之3）
- 滑点：`0.2`
- 合约乘数：`100`
- 最小变动价位：`0.01`

### 3. 数据要求

**重要：** 确保有足够的历史数据！
- 策略需要至少100天的历史数据来初始化ArrayManager
- 建议回测开始日期前至少有120天的数据
- 使用rqdata时，确保数据源配置正确

### 4. 预期结果

根据测试，策略应该：
- 在有足够数据后开始计算因子（约100天后）
- 产生买入/卖出信号（信号强度 > 0.01时）
- 执行相应的交易操作

## 🔧 故障排除

### 问题1：没有交易信号
**可能原因：**
- 历史数据不足（少于100天）
- 信号阈值过高
- 因子计算结果都接近0

**解决方案：**
```python
# 降低信号阈值
"signal_threshold": 0.005

# 减少回看天数
"lookback_days": 10

# 开启调试模式查看详细信息
"debug_mode": true
```

### 问题2：ArrayManager未初始化
**解决方案：**
- 确保回测开始日期前有足够的历史数据
- 检查数据源连接是否正常
- 验证合约代码是否正确

### 问题3：因子计算失败
**解决方案：**
- 检查factor_alpha101.py文件是否在正确位置
- 验证数据格式是否符合Alpha101要求
- 查看策略日志中的错误信息

## 📊 调试技巧

### 1. 开启调试模式
设置 `"debug_mode": true` 可以看到：
- 数据加载状态
- 因子计算结果
- 信号生成过程
- 交易执行详情

### 2. 查看关键日志
关注以下日志信息：
```
数据充足，开始因子计算。当前价格: XX.XX
因子计算完成: {'alpha001': X.X, 'alpha015': X.X, 'alpha030': X.X}
综合Alpha: X.XXXXXX, 信号: X, 当前仓位: X
发出买入/卖空信号: 价格=XX.XX, 数量=X
```

### 3. 参数调优建议
- **信号阈值**：从0.005开始，逐步调整
- **因子组合**：先用3个因子测试，再增加
- **持仓大小**：从1手开始测试
- **ATR周期**：使用10-20天

## 🚀 生产环境建议

1. **关闭调试模式**：`"debug_mode": false`
2. **适当的信号阈值**：`0.01-0.02`
3. **合理的仓位控制**：根据资金规模设置
4. **定期监控**：关注策略表现和市场变化

## 📈 预期表现

基于测试结果，策略能够：
- 识别市场趋势变化
- 在信号强度足够时执行交易
- 通过ATR进行风险控制
- 实现多因子量化交易

**注意：** 实际表现会受到市场环境、数据质量、参数设置等多种因素影响。建议先在模拟环境中充分测试后再用于实盘交易。
