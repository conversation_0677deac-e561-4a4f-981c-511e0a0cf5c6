
from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    ArrayManager,
)

import numpy as np
import pandas as pd
import datetime as dt

# 导入Alpha101因子模块
try:
    # 首先尝试相对导入
    from .factor_alpha101 import Alphas  # type: ignore
    FACTOR_MODULE_LOADED = True
except ImportError:
    try:
        # 如果相对导入失败，尝试绝对导入
        import factor_alpha101  # type: ignore
        Alphas = factor_alpha101.Alphas
        FACTOR_MODULE_LOADED = True
    except ImportError as e:
        FACTOR_MODULE_LOADED = False
        print(f"警告：无法导入factor_alpha101模块: {e}，将使用模拟因子")

    # 创建模拟的Alphas类
    class MockAlphas:
            def __init__(self, data):
                self.data = data

            def alpha001(self):
                """模拟alpha001因子"""
                if len(self.data) < 2:
                    return pd.Series([0])
                returns = self.data['S_DQ_CLOSE'].pct_change().fillna(0)
                return returns.rolling(5).mean()

            def alpha015(self):
                """模拟alpha015因子"""
                if len(self.data) < 2:
                    return pd.Series([0])
                high = self.data['S_DQ_HIGH']
                close = self.data['S_DQ_CLOSE']
                return (high - close).rolling(3).mean()

            def alpha030(self):
                """模拟alpha030因子"""
                if len(self.data) < 2:
                    return pd.Series([0])
                volume = self.data['S_DQ_VOLUME']
                return volume.rolling(10).mean()

            def alpha045(self):
                """模拟alpha045因子"""
                if len(self.data) < 2:
                    return pd.Series([0])
                close = self.data['S_DQ_CLOSE']
                return close.rolling(5).std()

            def alpha060(self):
                """模拟alpha060因子"""
                if len(self.data) < 2:
                    return pd.Series([0])
                volume = self.data['S_DQ_VOLUME']
                close = self.data['S_DQ_CLOSE']
                return (volume * close).rolling(7).mean()

    Alphas = MockAlphas


class AlphaStrategy(CtaTemplate):
    """基于Alpha101因子的多因子量化交易策略"""

    author = "AlphaTrader"

    # 策略参数 - Alpha101因子配置
    alpha_factors = ["alpha001", "alpha015", "alpha030", "alpha045", "alpha060"]  # 选择的Alpha因子
    factor_weights = [0.2, 0.2, 0.2, 0.2, 0.2]  # 各因子权重，和为1
    signal_threshold = 0.01      # 信号阈值（大幅降低以便更容易触发交易）
    lookback_days = 20           # 获取因子计算的历史数据天数（减少以适应回测）
    risk_percent = 0.02          # 风险比例（资金的百分比）
    atr_period = 14              # ATR计算周期（减少以适应更短的数据）
    atr_multiplier = 2.0         # ATR止损倍数

    # 交易参数
    price_offset = 0.002         # 限价单价格偏移（0.2%）
    max_position = 1             # 最大持仓手数（简化为1手）

    # 调试参数
    debug_mode = True            # 开启调试模式



    # 策略变量
    combined_alpha = 0.0         # 综合Alpha值
    signal = 0                   # 交易信号 (1:买入, -1:卖出, 0:无信号)
    last_factor_time = None      # 上次计算因子的时间
    current_atr = 0.0            # 当前ATR值

    # 参数列表
    parameters = [
        "alpha_factors", "factor_weights", "signal_threshold", "lookback_days",
        "risk_percent", "atr_period", "atr_multiplier", "price_offset", "max_position", "debug_mode"
    ]

    # 变量列表
    variables = [
        "combined_alpha", "signal", "current_atr"
    ]

    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        """策略初始化"""
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)

        # 处理alpha_factors参数
        if "alpha_factors" in setting:
            if isinstance(setting["alpha_factors"], list):
                # 确保列表中的元素都是字符串
                self.alpha_factors = [str(factor).strip().strip("'\"") for factor in setting["alpha_factors"]]
            elif isinstance(setting["alpha_factors"], str):
                # 处理字符串格式的因子列表
                cleaned = setting["alpha_factors"].strip("[]() ")
                if cleaned:
                    self.alpha_factors = [x.strip().strip("'\"") for x in cleaned.split(",") if x.strip()]
                else:
                    self.write_log("因子字符串为空，使用默认因子")
                    # 保持默认值不变
            else:
                self.write_log(f"因子参数类型不支持: {type(setting['alpha_factors'])}，使用默认因子")

        # 处理factor_weights参数
        if "factor_weights" in setting:
            if isinstance(setting["factor_weights"], list):
                # 处理列表中可能包含字符串的情况
                try:
                    self.factor_weights = [float(w) for w in setting["factor_weights"]]
                except (ValueError, TypeError):
                    # 如果列表中有非数字元素，尝试字符串解析
                    self.write_log(f"权重列表包含非数字元素，尝试字符串解析: {setting['factor_weights']}")
                    weight_str = str(setting["factor_weights"]).strip("[]() ")
                    try:
                        parts = weight_str.split(",")
                        self.factor_weights = [float(part.strip().strip("'\"")) for part in parts if part.strip()]
                    except ValueError:
                        self.write_log(f"无法解析权重，使用默认等权重")
                        self.factor_weights = [1.0/len(self.alpha_factors)] * len(self.alpha_factors)
            elif isinstance(setting["factor_weights"], str):
                cleaned = setting["factor_weights"].strip("[]() ")
                if cleaned:
                    try:
                        parts = cleaned.split(",")
                        self.factor_weights = [float(part.strip().strip("'\"")) for part in parts if part.strip()]
                    except ValueError:
                        self.write_log(f"无法解析权重: {cleaned}，使用默认等权重")
                        self.factor_weights = [1.0/len(self.alpha_factors)] * len(self.alpha_factors)
                else:
                    self.write_log("权重字符串为空，使用默认等权重")
                    self.factor_weights = [1.0/len(self.alpha_factors)] * len(self.alpha_factors)
            else:
                # 其他类型，使用默认等权重
                self.write_log(f"权重参数类型不支持: {type(setting['factor_weights'])}，使用默认等权重")
                self.factor_weights = [1.0/len(self.alpha_factors)] * len(self.alpha_factors)

        # 确保权重与因子数量相等并标准化
        if len(self.factor_weights) != len(self.alpha_factors):
            self.write_log(f"权重数量({len(self.factor_weights)})与因子数量({len(self.alpha_factors)})不匹配，使用等权重")
            self.factor_weights = [1.0/len(self.alpha_factors)] * len(self.alpha_factors)

        # 标准化权重，确保和为1
        weight_sum = sum(self.factor_weights)
        if weight_sum > 0:
            self.factor_weights = [w/weight_sum for w in self.factor_weights]



        # 初始化数据管理器
        self.bg = BarGenerator(self.on_bar)
        self.am = ArrayManager()



        # 初始化因子数据存储
        self.factor_values = {}
        self.factor_history = []

    def on_init(self):
        """策略初始化完成回调"""
        self.write_log("策略初始化")
        # 加载更多历史数据以确保因子计算有足够数据
        self.load_bar(max(50, self.lookback_days + 30))

    def on_start(self):
        """策略启动回调"""
        self.write_log("策略启动")
        self.put_event()

    def on_stop(self):
        """策略停止回调"""
        self.write_log("策略停止")
        self.put_event()

    def on_tick(self, tick: TickData):
        """Tick数据更新回调"""
        self.bg.update_tick(tick)

    def on_bar(self, bar: BarData):
        """K线数据更新回调 - 核心交易逻辑"""
        # 更新数据到数组管理器
        self.am.update_bar(bar)
        if not self.am.inited:
            if self.debug_mode:
                self.write_log("ArrayManager未初始化，等待更多数据...")
            return

        # 取消所有订单
        self.cancel_all()

        # 检查数据是否足够
        if len(self.am.close_array) < self.lookback_days:
            if self.debug_mode:
                self.write_log(f"历史数据不足: {len(self.am.close_array)}/{self.lookback_days}")
            return

        if self.debug_mode:
            self.write_log(f"数据充足，开始因子计算。当前价格: {bar.close_price}")

        # 检查是否需要更新Alpha因子
        current_time = bar.datetime
        if self._should_update_factors(current_time):
            try:
                self._calculate_alpha_factors()
                self.last_factor_time = current_time
                if self.debug_mode:
                    self.write_log(f"因子计算完成: {self.factor_values}")
            except Exception as e:
                self.write_log(f"Alpha因子计算失败: {e}")
                return

        # 计算综合Alpha信号
        if self.factor_values:
            self.combined_alpha = self._calculate_combined_alpha()
            self._generate_trading_signal()

            if self.debug_mode:
                self.write_log(f"综合Alpha: {self.combined_alpha:.6f}, 信号: {self.signal}, 当前仓位: {self.pos}")

            # 执行交易策略
            self._execute_trading_strategy(bar)
        else:
            if self.debug_mode:
                self.write_log("因子值为空，跳过交易")

        # 更新UI
        self.put_event()

    def on_order(self, order: OrderData):
        """订单状态更新回调"""
        pass

    def on_trade(self, trade: TradeData):
        """成交回报回调"""
        self.put_event()



    def on_stop_order(self, stop_order: StopOrder):
        """停止单状态更新回调"""
        pass

    def _should_update_factors(self, current_time):
        """判断是否需要更新因子"""
        if self.last_factor_time is None:
            return True

        # 每天更新一次因子
        if current_time.date() != self.last_factor_time.date():
            return True

        return False

    def _get_history_dataframe(self):
        """获取历史数据并转换为Alpha101所需的DataFrame格式"""
        try:
            # 从ArrayManager获取历史数据
            if not self.am.inited or len(self.am.close_array) < self.lookback_days:
                return None

            # 构建DataFrame
            data_length = min(self.lookback_days, len(self.am.close_array))

            # 转换为Alpha101期望的格式
            df = pd.DataFrame({
                'S_DQ_OPEN': self.am.open_array[-data_length:],
                'S_DQ_HIGH': self.am.high_array[-data_length:],
                'S_DQ_LOW': self.am.low_array[-data_length:],
                'S_DQ_CLOSE': self.am.close_array[-data_length:],
                'S_DQ_VOLUME': self.am.volume_array[-data_length:],
            })

            # 计算收益率和成交额（Alpha101需要）
            df['S_DQ_PCTCHANGE'] = df['S_DQ_CLOSE'].pct_change().fillna(0)
            # 假设成交额 = 收盘价 * 成交量（简化处理）
            df['S_DQ_AMOUNT'] = df['S_DQ_CLOSE'] * df['S_DQ_VOLUME'] / 1000  # 转换为千元单位

            # 添加索引
            df.index = range(len(df))

            return df

        except Exception as e:
            self.write_log(f"获取历史数据失败: {e}")
            return None

    def _calculate_alpha_factors(self):
        """计算Alpha101因子"""
        try:
            # 获取历史数据
            history_data = self._get_history_dataframe()
            if history_data is None or len(history_data) < self.lookback_days:
                raise ValueError(f"历史数据不足，需要{self.lookback_days}条，实际{len(history_data) if history_data is not None else 0}条")

            # 创建Alphas实例
            alphas_calculator = Alphas(history_data)

            # 清空之前的因子值
            self.factor_values = {}

            # 逐个计算选定的Alpha因子
            for factor_name in self.alpha_factors:
                try:
                    if hasattr(alphas_calculator, factor_name):
                        factor_func = getattr(alphas_calculator, factor_name)
                        factor_value = factor_func()

                        # 获取最新的因子值（通常是最后一个值）
                        if isinstance(factor_value, pd.Series):
                            latest_value = factor_value.iloc[-1] if len(factor_value) > 0 else 0
                        elif isinstance(factor_value, pd.DataFrame):
                            # 如果返回DataFrame，取最后一行的第一列
                            latest_value = factor_value.iloc[-1, 0] if len(factor_value) > 0 else 0
                        elif isinstance(factor_value, (int, float)):
                            latest_value = factor_value
                        else:
                            latest_value = 0

                        # 检查因子值是否有效
                        if pd.isna(latest_value) or np.isinf(latest_value):
                            self.write_log(f"因子{factor_name}计算结果无效: {latest_value}")
                            latest_value = 0

                        self.factor_values[factor_name] = float(latest_value)

                    else:
                        self.write_log(f"警告：Alpha101计算器中未找到{factor_name}方法")
                        self.factor_values[factor_name] = 0

                except Exception as e:
                    self.write_log(f"计算因子{factor_name}时出错: {e}")
                    self.factor_values[factor_name] = 0

            # 记录因子历史
            factor_record = {
                'datetime': dt.datetime.now(),
                'factors': self.factor_values.copy(),
                'combined_alpha': self.combined_alpha
            }
            self.factor_history.append(factor_record)

            # 保持历史记录不超过100条
            if len(self.factor_history) > 100:
                self.factor_history = self.factor_history[-100:]

        except Exception as e:
            self.write_log(f"Alpha因子计算失败: {e}")
            raise

    def _calculate_combined_alpha(self):
        """计算综合Alpha值"""
        if not self.factor_values:
            return 0.0

        # 收集所有因子值
        factor_list = []
        for factor in self.alpha_factors:
            if factor in self.factor_values:
                value = self.factor_values[factor]
                if not (pd.isna(value) or np.isinf(value)):
                    factor_list.append(value)
                else:
                    factor_list.append(0.0)
            else:
                factor_list.append(0.0)

        if not factor_list or all(v == 0 for v in factor_list):
            return 0.0

        # 简单的标准化：使用tanh函数将值映射到(-1, 1)区间
        normalized_factors = [np.tanh(v) for v in factor_list]

        # 按权重加权平均
        combined = sum(nf * w for nf, w in zip(normalized_factors, self.factor_weights))

        if self.debug_mode:
            self.write_log(f"原始因子值: {factor_list}")
            self.write_log(f"标准化因子值: {normalized_factors}")
            self.write_log(f"综合Alpha: {combined:.6f}")

        return combined

    def _generate_trading_signal(self):
        """生成交易信号"""
        if self.combined_alpha > self.signal_threshold:
            self.signal = 1  # 买入信号
        elif self.combined_alpha < -self.signal_threshold:
            self.signal = -1  # 卖出信号
        else:
            self.signal = 0  # 无信号



    def _execute_trading_strategy(self, bar: BarData):
        """执行交易策略"""
        try:
            # 计算ATR用于风险控制
            if len(self.am.close_array) >= self.atr_period:
                self.current_atr = self.am.atr(self.atr_period)
            else:
                # 如果数据不足，使用简单的价格波动率
                self.current_atr = np.std(self.am.close_array[-10:]) if len(self.am.close_array) >= 10 else bar.close_price * 0.02

            if self.current_atr <= 0:
                self.current_atr = bar.close_price * 0.01  # 设置最小ATR

            if self.debug_mode:
                self.write_log(f"ATR: {self.current_atr:.4f}")

            current_price = bar.close_price
            position_size = self.max_position  # 简化仓位计算

            # 简化的交易逻辑
            if self.signal == 1 and self.pos == 0:  # 买入信号且无持仓
                self.buy(current_price, position_size)
                if self.debug_mode:
                    self.write_log(f"发出买入信号: 价格={current_price}, 数量={position_size}")

            elif self.signal == -1 and self.pos == 0:  # 卖出信号且无持仓
                self.short(current_price, position_size)
                if self.debug_mode:
                    self.write_log(f"发出卖空信号: 价格={current_price}, 数量={position_size}")

            elif self.signal == 1 and self.pos < 0:  # 买入信号且有空头持仓
                # 先平空仓，再开多仓
                self.cover(current_price, abs(self.pos))
                self.buy(current_price, position_size)
                if self.debug_mode:
                    self.write_log(f"平空开多: 价格={current_price}")

            elif self.signal == -1 and self.pos > 0:  # 卖出信号且有多头持仓
                # 先平多仓，再开空仓
                self.sell(current_price, self.pos)
                self.short(current_price, position_size)
                if self.debug_mode:
                    self.write_log(f"平多开空: 价格={current_price}")

            # 简化的止损逻辑
            elif self.pos > 0:  # 多头持仓止损
                stop_price = current_price - self.current_atr * self.atr_multiplier
                if current_price <= stop_price:
                    self.sell(current_price, self.pos)
                    if self.debug_mode:
                        self.write_log(f"多头止损: 价格={current_price}, 止损价={stop_price:.4f}")

            elif self.pos < 0:  # 空头持仓止损
                stop_price = current_price + self.current_atr * self.atr_multiplier
                if current_price >= stop_price:
                    self.cover(current_price, abs(self.pos))
                    if self.debug_mode:
                        self.write_log(f"空头止损: 价格={current_price}, 止损价={stop_price:.4f}")

        except Exception as e:
            self.write_log(f"执行交易策略时出错: {e}")


