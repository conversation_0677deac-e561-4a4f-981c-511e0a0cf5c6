#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试策略参数输入解析功能
"""

import sys
import os

# 添加策略目录到路径
strategy_path = os.path.join(os.getcwd(), "Lib", "site-packages", "vnpy_ctastrategy", "strategies")
if strategy_path not in sys.path:
    sys.path.insert(0, strategy_path)

# 模拟VeighNa环境
class MockCtaEngine:
    def write_log(self, msg):
        print(f"   [LOG] {msg}")

class MockSetting:
    def __init__(self, **kwargs):
        self.data = kwargs
    
    def __contains__(self, key):
        return key in self.data
    
    def __getitem__(self, key):
        return self.data[key]

def test_input_parsing():
    """测试各种输入格式的解析"""
    print("=== 测试策略参数输入解析 ===\n")
    
    # 导入策略类
    try:
        # 直接导入策略模块
        import importlib.util
        spec = importlib.util.spec_from_file_location("MyStrategy",
            os.path.join(strategy_path, "MyStrategy.py"))
        strategy_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(strategy_module)
        AlphaStrategy = strategy_module.AlphaStrategy
        print("✓ 成功导入AlphaStrategy")
    except Exception as e:
        print(f"✗ 导入策略失败: {e}")
        return False
    
    # 测试用例
    test_cases = [
        {
            "name": "列表格式输入",
            "setting": {
                "alpha_factors": ["alpha001", "alpha002", "alpha003"],
                "factor_weights": [0.3, 0.4, 0.3]
            }
        },
        {
            "name": "字符串格式输入（带引号）",
            "setting": {
                "alpha_factors": "['alpha001', 'alpha002', 'alpha003']",
                "factor_weights": "[0.3, 0.4, 0.3]"
            }
        },
        {
            "name": "字符串格式输入（无引号）",
            "setting": {
                "alpha_factors": "[alpha001, alpha002, alpha003]",
                "factor_weights": "[0.3, 0.4, 0.3]"
            }
        },
        {
            "name": "逗号分隔字符串",
            "setting": {
                "alpha_factors": "alpha001, alpha002, alpha003",
                "factor_weights": "0.3, 0.4, 0.3"
            }
        },
        {
            "name": "权重数量不匹配（自动等权重）",
            "setting": {
                "alpha_factors": ["alpha001", "alpha002", "alpha003"],
                "factor_weights": [0.5, 0.5]  # 只有2个权重，但有3个因子
            }
        },
        {
            "name": "权重和不为1（自动标准化）",
            "setting": {
                "alpha_factors": ["alpha001", "alpha002"],
                "factor_weights": [2.0, 3.0]  # 和为5，应该标准化为[0.4, 0.6]
            }
        },
        {
            "name": "默认参数（无输入）",
            "setting": {}
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"{i}. 测试: {test_case['name']}")
        
        try:
            # 创建模拟设置
            setting = MockSetting(**test_case['setting'])
            
            # 创建策略实例
            strategy = AlphaStrategy(
                cta_engine=MockCtaEngine(),
                strategy_name="test_strategy",
                vt_symbol="test.TEST",
                setting=setting
            )
            
            print(f"   因子: {strategy.alpha_factors}")
            print(f"   权重: {[f'{w:.3f}' for w in strategy.factor_weights]}")
            print(f"   权重和: {sum(strategy.factor_weights):.6f}")
            
            # 验证权重和是否为1
            weight_sum = sum(strategy.factor_weights)
            if abs(weight_sum - 1.0) < 1e-6:
                print("   ✓ 权重和正确")
            else:
                print(f"   ✗ 权重和错误: {weight_sum}")
            
            # 验证因子和权重数量是否匹配
            if len(strategy.alpha_factors) == len(strategy.factor_weights):
                print("   ✓ 因子和权重数量匹配")
            else:
                print("   ✗ 因子和权重数量不匹配")
                
        except Exception as e:
            print(f"   ✗ 测试失败: {e}")
        
        print()
    
    # 测试其他参数
    print("7. 测试其他参数解析:")
    other_params = {
        "signal_threshold": 0.8,
        "lookback_days": 50,
        "risk_percent": 0.03,
        "atr_period": 14,
        "atr_multiplier": 1.5,
        "price_offset": 0.001,
        "max_position": 10
    }
    
    try:
        setting = MockSetting(**other_params)
        strategy = AlphaStrategy(
            cta_engine=MockCtaEngine(),
            strategy_name="test_strategy",
            vt_symbol="test.TEST",
            setting=setting
        )
        
        print(f"   信号阈值: {strategy.signal_threshold}")
        print(f"   历史天数: {strategy.lookback_days}")
        print(f"   风险比例: {strategy.risk_percent}")
        print(f"   ATR周期: {strategy.atr_period}")
        print(f"   ATR倍数: {strategy.atr_multiplier}")
        print(f"   价格偏移: {strategy.price_offset}")
        print(f"   最大仓位: {strategy.max_position}")
        print("   ✓ 其他参数解析正确")
        
    except Exception as e:
        print(f"   ✗ 其他参数解析失败: {e}")
    
    print("\n=== 测试完成 ===")
    return True

def test_factor_availability():
    """测试因子可用性"""
    print("\n=== 测试因子可用性 ===")
    
    try:
        import factor_alpha101
        alphas_class = factor_alpha101.Alphas
        
        # 创建测试数据
        import pandas as pd
        import numpy as np
        
        test_data = pd.DataFrame({
            'S_DQ_OPEN': np.random.rand(30) * 100,
            'S_DQ_HIGH': np.random.rand(30) * 100,
            'S_DQ_LOW': np.random.rand(30) * 100,
            'S_DQ_CLOSE': np.random.rand(30) * 100,
            'S_DQ_VOLUME': np.random.randint(1000, 10000, 30),
            'S_DQ_PCTCHANGE': np.random.rand(30) * 0.1 - 0.05,
            'S_DQ_AMOUNT': np.random.rand(30) * 1000000
        })
        
        alphas = alphas_class(test_data)
        
        # 测试常用因子
        common_factors = [
            'alpha001', 'alpha002', 'alpha003', 'alpha004', 'alpha005',
            'alpha010', 'alpha020', 'alpha030', 'alpha040', 'alpha050',
            'alpha060', 'alpha070', 'alpha080', 'alpha090', 'alpha101'
        ]
        
        available_factors = []
        unavailable_factors = []
        
        for factor in common_factors:
            if hasattr(alphas, factor):
                try:
                    method = getattr(alphas, factor)
                    result = method()
                    available_factors.append(factor)
                except Exception as e:
                    unavailable_factors.append(f"{factor} (计算错误: {str(e)[:50]})")
            else:
                unavailable_factors.append(f"{factor} (方法不存在)")
        
        print(f"可用因子 ({len(available_factors)}个):")
        for factor in available_factors:
            print(f"   ✓ {factor}")
        
        if unavailable_factors:
            print(f"\n不可用因子 ({len(unavailable_factors)}个):")
            for factor in unavailable_factors:
                print(f"   ✗ {factor}")
        
        print(f"\n总计: {len(available_factors)}/{len(common_factors)} 个因子可用")
        
    except Exception as e:
        print(f"✗ 因子可用性测试失败: {e}")

if __name__ == "__main__":
    success = test_input_parsing()
    test_factor_availability()
    
    if success:
        print("\n🎉 输入解析测试通过！")
    else:
        print("\n❌ 输入解析测试失败！")
