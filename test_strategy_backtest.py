#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AlphaStrategy回测功能
"""

import sys
import os
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

# 添加策略目录到路径
strategy_path = os.path.join(os.getcwd(), "Lib", "site-packages", "vnpy_ctastrategy", "strategies")
if strategy_path not in sys.path:
    sys.path.insert(0, strategy_path)

# 导入VeighNa相关模块
from vnpy.trader.constant import Interval, Exchange
from vnpy.trader.object import BarData
from vnpy_ctastrategy.backtesting import BacktestingEngine
from vnpy_ctastrategy.strategies.MyStrategy import AlphaStrategy

def test_strategy_backtest():
    """测试策略回测"""
    print("=== 开始AlphaStrategy回测测试 ===\n")
    
    # 创建回测引擎
    engine = BacktestingEngine()
    
    # 设置回测参数
    engine.set_parameters(
        vt_symbol="000001.SZSE",  # 平安银行
        interval=Interval.DAILY,
        start=datetime(2023, 1, 1),
        end=datetime(2023, 12, 31),
        rate=3/10000,  # 手续费率
        slippage=0.2,  # 滑点
        size=100,      # 合约乘数
        pricetick=0.01,  # 最小价格变动
        capital=100000,  # 初始资金
    )
    
    # 添加策略
    engine.add_strategy(AlphaStrategy, {
        "alpha_factors": ["alpha001", "alpha015", "alpha030"],  # 使用较少的因子
        "factor_weights": [0.33, 0.33, 0.34],
        "signal_threshold": 0.01,  # 很低的阈值
        "lookback_days": 15,       # 较短的回看期
        "debug_mode": True,        # 开启调试
        "max_position": 1,         # 1手
        "atr_period": 10,          # 较短的ATR周期
    })
    
    print("策略配置完成，开始加载数据...")
    
    try:
        # 加载数据
        engine.load_data()
        print("数据加载完成")
        
        # 运行回测
        print("开始运行回测...")
        engine.run_backtesting()
        
        # 获取回测结果
        df = engine.calculate_result()
        statistics = engine.calculate_statistics()
        
        print("\n=== 回测结果 ===")
        print(f"总收益率: {statistics['total_return']:.2%}")
        print(f"年化收益率: {statistics['annual_return']:.2%}")
        print(f"最大回撤: {statistics['max_drawdown']:.2%}")
        print(f"夏普比率: {statistics['sharpe_ratio']:.2f}")
        print(f"总交易次数: {statistics['total_trade_count']}")
        print(f"盈利交易次数: {statistics['winning_trade_count']}")
        print(f"胜率: {statistics['win_rate']:.2%}")
        
        # 显示交易记录
        trades = engine.get_all_trades()
        if trades:
            print(f"\n前5笔交易记录:")
            for i, trade in enumerate(trades[:5]):
                print(f"  {i+1}. {trade.datetime} {trade.direction.value} {trade.volume}手 @{trade.price}")
        else:
            print("\n⚠️  没有产生任何交易记录")
            
        return True
        
    except Exception as e:
        print(f"回测失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_sample_data():
    """创建样本数据用于测试"""
    print("创建样本数据...")
    
    # 生成模拟的股价数据
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    dates = [d for d in dates if d.weekday() < 5]  # 只保留工作日
    
    np.random.seed(42)
    n_days = len(dates)
    
    # 生成价格数据
    base_price = 10.0
    returns = np.random.normal(0, 0.02, n_days)  # 日收益率
    prices = [base_price]
    
    for ret in returns[1:]:
        new_price = prices[-1] * (1 + ret)
        prices.append(max(new_price, 0.1))  # 确保价格为正
    
    # 生成OHLC数据
    bars = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        # 生成开高低价
        open_price = close * (1 + np.random.normal(0, 0.005))
        high_price = max(open_price, close) * (1 + abs(np.random.normal(0, 0.01)))
        low_price = min(open_price, close) * (1 - abs(np.random.normal(0, 0.01)))
        volume = np.random.randint(1000000, 10000000)
        
        bar = BarData(
            symbol="000001",
            exchange=Exchange.SZSE,
            datetime=date,
            interval=Interval.DAILY,
            volume=volume,
            turnover=volume * close,
            open_price=open_price,
            high_price=high_price,
            low_price=low_price,
            close_price=close,
            gateway_name="test"
        )
        bars.append(bar)
    
    print(f"生成了{len(bars)}条K线数据")
    return bars

if __name__ == "__main__":
    # 首先测试因子计算
    try:
        from factor_alpha101 import Alphas
        print("✓ factor_alpha101模块导入成功")
    except ImportError as e:
        print(f"✗ factor_alpha101模块导入失败: {e}")
        sys.exit(1)
    
    # 运行回测测试
    success = test_strategy_backtest()
    
    if success:
        print("\n🎉 策略回测测试完成！")
    else:
        print("\n❌ 策略回测测试失败！")
