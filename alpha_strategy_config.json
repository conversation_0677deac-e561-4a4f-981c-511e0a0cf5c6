{"strategy_name": "AlphaStrategy", "class_name": "AlphaStrategy", "vt_symbol": "000001.SZSE", "setting": {"alpha_factors": ["alpha001", "alpha015", "alpha030"], "factor_weights": [0.33, 0.33, 0.34], "signal_threshold": 0.01, "lookback_days": 15, "risk_percent": 0.02, "atr_period": 10, "atr_multiplier": 2.0, "price_offset": 0.002, "max_position": 1, "debug_mode": true}, "backtesting_settings": {"start_date": "2023-01-01", "end_date": "2023-12-31", "initial_capital": 100000, "commission_rate": 0.0003, "slippage": 0.2, "size": 100, "pricetick": 0.01}, "recommended_symbols": ["000001.SZSE", "000002.SZSE", "600000.SSE", "600036.SSE", "000858.SZSE"], "notes": {"signal_threshold": "信号阈值，建议0.005-0.02之间", "lookback_days": "历史数据天数，建议10-30天", "debug_mode": "调试模式，生产环境建议设为false", "max_position": "最大持仓手数，建议从1开始测试"}}