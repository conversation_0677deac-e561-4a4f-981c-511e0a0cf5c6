#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AlphaStrategy策略的基本功能
"""

import sys
import os
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

# 添加VeighNa路径
sys.path.insert(0, r'c:\veighna_studio\Lib\site-packages')

from vnpy_ctastrategy.backtesting import BacktestingEngine
from vnpy_ctastrategy.strategies.MyStrategy import AlphaStrategy
from vnpy_ctastrategy.base import BacktestingMode
from vnpy.trader.constant import Interval, Exchange
from vnpy.trader.object import BarData

def create_test_data():
    """创建测试用的K线数据"""
    print("创建测试数据...")
    
    # 生成100天的测试数据
    dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
    
    # 模拟价格数据
    np.random.seed(42)  # 固定随机种子
    base_price = 100.0
    price_changes = np.random.normal(0, 0.02, 100)  # 2%的日波动
    
    prices = [base_price]
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 1.0))  # 确保价格不为负
    
    bars = []
    for i, date in enumerate(dates):
        # 生成OHLC数据
        close = prices[i]
        high = close * (1 + abs(np.random.normal(0, 0.01)))
        low = close * (1 - abs(np.random.normal(0, 0.01)))
        open_price = close * (1 + np.random.normal(0, 0.005))
        volume = np.random.randint(1000, 10000)
        
        bar = BarData(
            symbol="TEST",
            exchange=Exchange.SSE,
            datetime=datetime.combine(date.date(), datetime.min.time()),
            interval=Interval.DAILY,
            volume=volume,
            turnover=close * volume,
            open_price=open_price,
            high_price=high,
            low_price=low,
            close_price=close,
            gateway_name="TEST"
        )
        bars.append(bar)
    
    print(f"生成了{len(bars)}条测试数据")
    return bars

def test_strategy():
    """测试策略"""
    print("开始测试AlphaStrategy...")
    
    # 创建回测引擎
    engine = BacktestingEngine()
    
    # 设置回测参数
    engine.set_parameters(
        vt_symbol="TEST.SSE",
        interval=Interval.DAILY,
        start=datetime(2023, 1, 1),
        end=datetime(2023, 4, 10),
        rate=0.0002,  # 手续费
        slippage=0.001,  # 滑点
        size=1,  # 合约乘数
        pricetick=0.01,  # 最小价格变动
        capital=100000,  # 初始资金
        mode=BacktestingMode.BAR  # 明确指定K线模式
    )
    
    # 策略参数
    strategy_setting = {
        "alpha_factors": ["alpha001", "alpha015", "alpha030"],
        "factor_weights": [0.33, 0.33, 0.34],
        "signal_threshold": 0.05,  # 降低阈值以便更容易触发信号
        "lookback_days": 20,
        "debug_mode": True,
        "debug_interval": 5
    }
    
    # 添加策略
    engine.add_strategy(AlphaStrategy, strategy_setting)
    
    # 创建并加载测试数据
    test_bars = create_test_data()
    engine.history_data = test_bars
    
    print("开始回测...")
    try:
        # 运行回测
        engine.run_backtesting()
        
        # 计算结果
        df = engine.calculate_result()
        
        # 输出结果
        print("\n" + "="*50)
        print("回测结果:")
        print("="*50)
        
        if engine.trades:
            print(f"总交易次数: {len(engine.trades)}")
            for trade_id, trade in engine.trades.items():
                print(f"交易 {trade_id}: {trade.direction.value} {trade.volume}手 @ {trade.price:.2f}")
        else:
            print("没有产生交易记录")
        
        print(f"\n策略日志 (最后10条):")
        for log in engine.logs[-10:]:
            print(log)
            
        if df is not None and not df.empty:
            print(f"\n回测统计:")
            print(f"总收益率: {df['total_return'].iloc[-1]:.2%}")
            print(f"年化收益率: {df['annual_return'].iloc[-1]:.2%}")
            print(f"最大回撤: {df['max_drawdown'].iloc[-1]:.2%}")
            print(f"夏普比率: {df['sharpe_ratio'].iloc[-1]:.2f}")
        
    except Exception as e:
        print(f"回测过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        
        # 输出策略日志以便调试
        print(f"\n策略日志:")
        for log in engine.logs:
            print(log)

if __name__ == "__main__":
    test_strategy()
