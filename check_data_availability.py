#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查VeighNa Studio中的数据可用性
"""

import sys
from datetime import datetime, timedelta

# 添加VeighNa路径
sys.path.insert(0, r'c:\veighna_studio\Lib\site-packages')

from vnpy.trader.database import get_database
from vnpy.trader.datafeed import get_datafeed
from vnpy.trader.constant import Exchange, Interval
from vnpy.trader.object import HistoryRequest

def check_database_data():
    """检查数据库中的数据"""
    print("=== 检查数据库中的数据 ===")
    
    database = get_database()
    
    # 检查399905.SZSE的数据
    symbol = "399905"
    exchange = Exchange.SZSE
    
    try:
        # 查询最近30天的数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        bars = database.load_bar_data(
            symbol=symbol,
            exchange=exchange,
            interval=Interval.DAILY,
            start=start_date,
            end=end_date
        )
        
        if bars:
            print(f"数据库中找到 {symbol}.{exchange.value}: {len(bars)} 条日线数据")
            print(f"  数据时间范围: {bars[0].datetime} 到 {bars[-1].datetime}")
            print(f"  最新数据: 收盘价 {bars[-1].close_price:.2f}, 成交量 {bars[-1].volume}")
            
            # 显示最近5天的数据
            print("  最近5天数据:")
            for bar in bars[-5:]:
                print(f"    {bar.datetime.strftime('%Y-%m-%d')}: 开{bar.open_price:.2f} 高{bar.high_price:.2f} 低{bar.low_price:.2f} 收{bar.close_price:.2f} 量{bar.volume}")
        else:
            print(f"数据库中没有找到 {symbol}.{exchange.value} 的数据")
            
        # 检查tick数据
        ticks = database.load_tick_data(
            symbol=symbol,
            exchange=exchange,
            start=start_date,
            end=end_date
        )
        
        if ticks:
            print(f"数据库中找到 {symbol}.{exchange.value}: {len(ticks)} 条tick数据")
            print(f"  最新tick: {ticks[-1].datetime} 价格 {ticks[-1].last_price:.2f}")
        else:
            print(f"数据库中没有找到 {symbol}.{exchange.value} 的tick数据")
            
    except Exception as e:
        print(f"查询数据库数据失败: {e}")

def check_datafeed():
    """检查数据源"""
    print("\n=== 检查数据源配置 ===")
    
    try:
        datafeed = get_datafeed()
        print(f"数据源类型: {type(datafeed).__name__}")
        
        # 尝试初始化数据源
        if hasattr(datafeed, 'init'):
            init_result = datafeed.init()
            print(f"数据源初始化结果: {init_result}")
        
        # 尝试查询历史数据
        symbol = "399905"
        exchange = Exchange.SZSE
        end_date = datetime.now()
        start_date = end_date - timedelta(days=10)
        
        req = HistoryRequest(
            symbol=symbol,
            exchange=exchange,
            interval=Interval.DAILY,
            start=start_date,
            end=end_date
        )
        
        print(f"尝试从数据源查询 {symbol}.{exchange.value} 的历史数据...")
        bars = datafeed.query_bar_history(req)
        
        if bars:
            print(f"数据源返回 {len(bars)} 条数据")
            print(f"  最新数据: {bars[-1].datetime} 收盘价 {bars[-1].close_price:.2f}")
        else:
            print("数据源没有返回数据")
            
        # 尝试查询tick数据
        print(f"尝试从数据源查询 {symbol}.{exchange.value} 的tick数据...")
        ticks = datafeed.query_tick_history(req)
        
        if ticks:
            print(f"数据源返回 {len(ticks)} 条tick数据")
            print(f"  最新tick: {ticks[-1].datetime} 价格 {ticks[-1].last_price:.2f}")
        else:
            print("数据源没有返回tick数据")
            
    except Exception as e:
        print(f"检查数据源失败: {e}")

def check_available_symbols():
    """检查可用的合约代码"""
    print("\n=== 检查数据库中的所有合约 ===")
    
    try:
        database = get_database()
        
        # 尝试查询数据库中的所有数据概览
        if hasattr(database, 'get_bar_overview'):
            overviews = database.get_bar_overview()
            if overviews:
                print("数据库中的合约概览:")
                for overview in overviews[:10]:  # 只显示前10个
                    print(f"  {overview.symbol}.{overview.exchange.value}: {overview.interval.value} "
                          f"从 {overview.start} 到 {overview.end}, {overview.count} 条数据")
            else:
                print("数据库中没有找到数据概览")
        else:
            print("数据库不支持概览查询")
            
    except Exception as e:
        print(f"查询数据概览失败: {e}")

if __name__ == "__main__":
    check_database_data()
    check_datafeed()
    check_available_symbols()
