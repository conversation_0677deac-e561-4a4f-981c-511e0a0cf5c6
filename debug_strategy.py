#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试策略问题 - 检查为什么没有交易信号
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加策略目录到路径
strategy_path = os.path.join(os.getcwd(), "Lib", "site-packages", "vnpy_ctastrategy", "strategies")
if strategy_path not in sys.path:
    sys.path.insert(0, strategy_path)

def test_factor_calculation():
    """测试因子计算"""
    print("=== 测试Alpha101因子计算 ===\n")
    
    try:
        import factor_alpha101
        print("✓ 成功导入factor_alpha101模块")
    except ImportError as e:
        print(f"✗ 导入factor_alpha101失败: {e}")
        return False
    
    # 创建模拟数据
    dates = pd.date_range(start='2023-01-01', periods=50, freq='D')
    np.random.seed(42)  # 固定随机种子以便重现
    
    # 生成模拟的股价数据
    base_price = 100
    returns = np.random.normal(0, 0.02, 50)  # 日收益率
    prices = [base_price]
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # 创建OHLCV数据
    df = pd.DataFrame({
        'S_DQ_OPEN': [p * (1 + np.random.normal(0, 0.005)) for p in prices],
        'S_DQ_HIGH': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'S_DQ_LOW': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'S_DQ_CLOSE': prices,
        'S_DQ_VOLUME': np.random.randint(1000, 10000, 50),
    }, index=dates)
    
    # 计算收益率和成交额
    df['S_DQ_PCTCHANGE'] = df['S_DQ_CLOSE'].pct_change().fillna(0)
    df['S_DQ_AMOUNT'] = df['S_DQ_CLOSE'] * df['S_DQ_VOLUME'] / 1000
    
    print(f"创建模拟数据: {len(df)} 条记录")
    print(f"价格范围: {df['S_DQ_CLOSE'].min():.2f} - {df['S_DQ_CLOSE'].max():.2f}")
    print(f"成交量范围: {df['S_DQ_VOLUME'].min()} - {df['S_DQ_VOLUME'].max()}")
    
    # 测试Alpha因子计算
    try:
        alphas = factor_alpha101.Alphas(df)
        print("✓ 成功创建Alphas实例")
        
        # 测试几个常用因子
        test_factors = ['alpha001', 'alpha002', 'alpha003', 'alpha004', 'alpha005']
        factor_results = {}
        
        for factor_name in test_factors:
            try:
                if hasattr(alphas, factor_name):
                    factor_func = getattr(alphas, factor_name)
                    factor_value = factor_func()
                    
                    # 获取最新值
                    if isinstance(factor_value, pd.Series):
                        latest_value = factor_value.iloc[-1] if len(factor_value) > 0 else 0
                    elif isinstance(factor_value, (int, float)):
                        latest_value = factor_value
                    else:
                        latest_value = 0
                    
                    if pd.isna(latest_value) or np.isinf(latest_value):
                        latest_value = 0
                    
                    factor_results[factor_name] = float(latest_value)
                    print(f"✓ {factor_name}: {latest_value:.6f}")
                else:
                    print(f"✗ {factor_name}: 方法不存在")
            except Exception as e:
                print(f"✗ {factor_name}: 计算失败 - {e}")
        
        # 测试综合信号计算
        if factor_results:
            print(f"\n计算综合信号:")
            weights = [0.2] * len(factor_results)  # 等权重
            
            # 标准化因子值
            normalized_factors = {}
            for factor, value in factor_results.items():
                if abs(value) > 1e-10:
                    normalized_factors[factor] = max(min(value/abs(value), 1), -1)
                else:
                    normalized_factors[factor] = 0
            
            # 计算加权平均
            combined_alpha = sum(normalized_factors[factor] * weight 
                               for factor, weight in zip(normalized_factors.keys(), weights))
            
            print(f"标准化因子值: {normalized_factors}")
            print(f"综合Alpha: {combined_alpha:.6f}")
            
            # 测试信号生成
            signal_threshold = 0.1
            if combined_alpha > signal_threshold:
                signal = 1  # 买入
            elif combined_alpha < -signal_threshold:
                signal = -1  # 卖出
            else:
                signal = 0  # 无信号
            
            print(f"信号阈值: ±{signal_threshold}")
            print(f"生成信号: {signal} ({'买入' if signal == 1 else '卖出' if signal == -1 else '无信号'})")
            
            return True
        else:
            print("✗ 没有成功计算任何因子")
            return False
            
    except Exception as e:
        print(f"✗ Alpha因子计算失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_strategy_parameters():
    """测试策略参数"""
    print("\n=== 测试策略参数 ===\n")
    
    # 模拟策略参数
    params = {
        "alpha_factors": ["alpha001", "alpha002", "alpha003", "alpha004", "alpha005"],
        "factor_weights": [0.2, 0.2, 0.2, 0.2, 0.2],
        "signal_threshold": 0.1,  # 降低的阈值
        "lookback_days": 30,
        "risk_percent": 0.02,
        "atr_period": 20,
        "atr_multiplier": 2.0,
        "price_offset": 0.002,
        "max_position": 5
    }
    
    print("策略参数:")
    for key, value in params.items():
        print(f"  {key}: {value}")
    
    # 检查参数合理性
    print(f"\n参数检查:")
    print(f"✓ 因子数量: {len(params['alpha_factors'])}")
    print(f"✓ 权重数量: {len(params['factor_weights'])}")
    print(f"✓ 权重和: {sum(params['factor_weights']):.3f}")
    print(f"✓ 信号阈值: {params['signal_threshold']} (较低，容易触发)")
    print(f"✓ 历史数据天数: {params['lookback_days']}")
    
    return True

def show_debugging_tips():
    """显示调试建议"""
    print("\n=== 调试建议 ===\n")
    
    tips = [
        "1. 检查VeighNa回测日志，查看是否有错误信息",
        "2. 确认历史数据是否充足（至少需要30+条K线数据）",
        "3. 检查信号阈值是否过高（建议设置为0.1或更低）",
        "4. 验证因子计算是否返回有效数值",
        "5. 确认ATR计算是否正常（ATR > 0）",
        "6. 检查价格偏移设置是否合理",
        "7. 验证仓位计算逻辑",
        "8. 查看策略日志中的调试信息"
    ]
    
    for tip in tips:
        print(f"  {tip}")
    
    print(f"\n常见问题解决方案:")
    print(f"  • 如果因子计算失败：检查数据格式和数据质量")
    print(f"  • 如果没有交易信号：降低signal_threshold参数")
    print(f"  • 如果ATR无效：检查历史数据是否足够")
    print(f"  • 如果仓位计算错误：检查risk_percent和ATR设置")

if __name__ == "__main__":
    print("🔍 开始调试策略问题...\n")
    
    # 测试因子计算
    factor_test_passed = test_factor_calculation()
    
    # 测试策略参数
    param_test_passed = test_strategy_parameters()
    
    # 显示调试建议
    show_debugging_tips()
    
    print(f"\n📊 测试结果:")
    print(f"  因子计算测试: {'✓ 通过' if factor_test_passed else '✗ 失败'}")
    print(f"  参数测试: {'✓ 通过' if param_test_passed else '✗ 失败'}")
    
    if factor_test_passed and param_test_passed:
        print(f"\n🎉 基础功能测试通过！")
        print(f"   建议在VeighNa中使用以下参数进行回测：")
        print(f"   - signal_threshold: 0.1 (或更低)")
        print(f"   - lookback_days: 30")
        print(f"   - 确保有足够的历史数据")
    else:
        print(f"\n❌ 存在问题，请检查上述错误信息")
