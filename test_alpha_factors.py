#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Alpha101因子计算功能
"""

import sys
import os
import pandas as pd
import numpy as np

# 添加策略目录到路径
strategy_path = os.path.join(os.getcwd(), "Lib", "site-packages", "vnpy_ctastrategy", "strategies")
if strategy_path not in sys.path:
    sys.path.insert(0, strategy_path)

try:
    import factor_alpha101
    print("✓ 成功导入factor_alpha101模块")
except ImportError as e:
    print(f"✗ 导入factor_alpha101模块失败: {e}")
    sys.exit(1)

def create_test_data(days=50):
    """创建测试数据"""
    np.random.seed(42)  # 固定随机种子以便重现
    
    # 生成模拟的股票数据
    dates = pd.date_range(start='2023-01-01', periods=days, freq='D')
    
    # 初始价格
    initial_price = 100.0
    prices = [initial_price]
    
    # 生成价格序列（随机游走）
    for i in range(1, days):
        change = np.random.normal(0, 0.02)  # 2%的日波动率
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 1.0))  # 确保价格不为负
    
    # 生成OHLC数据
    data = []
    for i, price in enumerate(prices):
        # 生成当日的高低开收
        daily_volatility = 0.01  # 1%的日内波动
        high = price * (1 + np.random.uniform(0, daily_volatility))
        low = price * (1 - np.random.uniform(0, daily_volatility))
        
        if i == 0:
            open_price = price
        else:
            open_price = prices[i-1] * (1 + np.random.normal(0, 0.005))  # 跳空
        
        close_price = price
        volume = np.random.randint(1000000, 10000000)  # 100万到1000万成交量
        
        data.append({
            'S_DQ_OPEN': open_price,
            'S_DQ_HIGH': high,
            'S_DQ_LOW': low,
            'S_DQ_CLOSE': close_price,
            'S_DQ_VOLUME': volume,
        })
    
    df = pd.DataFrame(data)
    
    # 计算收益率和成交额
    df['S_DQ_PCTCHANGE'] = df['S_DQ_CLOSE'].pct_change().fillna(0)
    df['S_DQ_AMOUNT'] = df['S_DQ_CLOSE'] * df['S_DQ_VOLUME'] / 1000  # 转换为千元单位
    
    return df

def test_alpha_factors():
    """测试Alpha因子计算"""
    print("\n=== 开始测试Alpha101因子计算 ===")
    
    # 创建测试数据
    print("1. 创建测试数据...")
    test_data = create_test_data(50)
    print(f"   生成了{len(test_data)}天的测试数据")
    print(f"   数据列: {list(test_data.columns)}")
    print(f"   价格范围: {test_data['S_DQ_CLOSE'].min():.2f} - {test_data['S_DQ_CLOSE'].max():.2f}")
    
    # 创建Alpha计算器
    print("\n2. 创建Alpha101计算器...")
    try:
        alphas = factor_alpha101.Alphas(test_data)
        print("   ✓ 成功创建Alpha101计算器")
    except Exception as e:
        print(f"   ✗ 创建Alpha101计算器失败: {e}")
        return False
    
    # 测试几个基础因子
    test_factors = ['alpha001', 'alpha002', 'alpha003', 'alpha004', 'alpha005']
    print(f"\n3. 测试因子计算 ({', '.join(test_factors)})...")
    
    results = {}
    for factor_name in test_factors:
        try:
            if hasattr(alphas, factor_name):
                factor_func = getattr(alphas, factor_name)
                factor_value = factor_func()
                
                # 获取最新值
                if isinstance(factor_value, pd.Series):
                    latest_value = factor_value.iloc[-1] if len(factor_value) > 0 else 0
                elif isinstance(factor_value, pd.DataFrame):
                    latest_value = factor_value.iloc[-1, 0] if len(factor_value) > 0 else 0
                elif isinstance(factor_value, (int, float)):
                    latest_value = factor_value
                else:
                    latest_value = 0
                
                # 检查有效性
                if pd.isna(latest_value) or np.isinf(latest_value):
                    latest_value = 0
                
                results[factor_name] = float(latest_value)
                print(f"   ✓ {factor_name}: {latest_value:.6f}")
                
            else:
                print(f"   ✗ {factor_name}: 方法不存在")
                results[factor_name] = 0
                
        except Exception as e:
            print(f"   ✗ {factor_name}: 计算失败 - {e}")
            results[factor_name] = 0
    
    # 计算综合Alpha
    print("\n4. 计算综合Alpha值...")
    weights = [0.2, 0.2, 0.2, 0.2, 0.2]  # 等权重
    combined_alpha = sum(results[factor] * weight for factor, weight in zip(test_factors, weights))
    print(f"   综合Alpha: {combined_alpha:.6f}")
    
    # 生成交易信号
    signal_threshold = 0.1
    if combined_alpha > signal_threshold:
        signal = "买入"
    elif combined_alpha < -signal_threshold:
        signal = "卖出"
    else:
        signal = "持有"
    
    print(f"   交易信号: {signal} (阈值: ±{signal_threshold})")
    
    print("\n=== 测试完成 ===")
    return True

if __name__ == "__main__":
    success = test_alpha_factors()
    if success:
        print("\n🎉 所有测试通过！Alpha101因子计算功能正常。")
    else:
        print("\n❌ 测试失败，请检查配置。")
