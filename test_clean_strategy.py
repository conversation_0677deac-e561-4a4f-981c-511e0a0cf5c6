#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试清理后的AlphaStrategy策略
"""

import sys
from datetime import datetime

# 添加VeighNa路径
sys.path.insert(0, r'c:\veighna_studio\Lib\site-packages')

from vnpy_ctastrategy.backtesting import BacktestingEngine
from vnpy.trader.constant import Interval
from vnpy_ctastrategy.strategies.MyStrategy import AlphaStrategy

def test_clean_strategy():
    """测试清理后的策略"""
    print("开始测试清理后的AlphaStrategy...")
    
    try:
        # 创建回测引擎
        engine = BacktestingEngine()
        
        # 设置回测参数
        engine.set_parameters(
            vt_symbol="cu2509.SHFE",
            interval=Interval.DAILY,
            start=datetime(2024, 12, 1),
            end=datetime(2025, 6, 20),
            rate=0.0003,  # 手续费率
            slippage=0.2,  # 滑点
            size=5,       # 合约乘数
            pricetick=10, # 最小价格变动
            capital=100000,  # 初始资金
        )
        
        # 策略参数
        strategy_setting = {
            "alpha_factors": ["alpha001", "alpha015", "alpha030"],
            "factor_weights": [0.33, 0.33, 0.34],
            "signal_threshold": 0.02,
            "lookback_days": 20,
            "risk_percent": 0.02,
            "max_position": 5
        }
        
        # 添加策略
        engine.add_strategy(AlphaStrategy, strategy_setting)
        
        # 加载数据
        print("加载历史数据...")
        engine.load_data()
        
        # 运行回测
        print("开始回测...")
        engine.run_backtesting()
        
        # 计算统计数据
        df = engine.calculate_result()
        statistics = engine.calculate_statistics()
        
        # 输出结果
        print("\n" + "="*60)
        print("回测结果:")
        print("="*60)
        
        if engine.trades:
            print(f"总交易次数: {len(engine.trades)}")
            print("\n交易记录:")
            for i, (trade_id, trade) in enumerate(list(engine.trades.items())[:10]):  # 只显示前10笔
                direction = "买入" if trade.direction.value == "多" else "卖出"
                print(f"  {i+1}. {trade.datetime.strftime('%Y-%m-%d')} {direction} {trade.volume}手 @ {trade.price:.2f}")
        else:
            print("无交易记录")
        
        # 显示关键统计数据
        if statistics:
            print(f"\n策略统计:")
            print(f"总收益率: {statistics.get('total_return', 0):.2%}")
            print(f"年化收益率: {statistics.get('annual_return', 0):.2%}")
            print(f"最大回撤: {statistics.get('max_drawdown', 0):.2%}")
            print(f"夏普比率: {statistics.get('sharpe_ratio', 0):.2f}")
        
        print("\n策略清理完成，运行正常！")
        return True
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_clean_strategy()
    if success:
        print("\n✅ 策略清理成功，所有功能正常！")
    else:
        print("\n❌ 策略测试失败，请检查代码")
