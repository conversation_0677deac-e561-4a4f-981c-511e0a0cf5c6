#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用VeighNa Studio中的真实数据测试AlphaStrategy策略
"""

import sys
from datetime import datetime, timedelta

# 添加VeighNa路径
sys.path.insert(0, r'c:\veighna_studio\Lib\site-packages')

from vnpy_ctastrategy.backtesting import BacktestingEngine
from vnpy_ctastrategy.strategies.MyStrategy import AlphaStrategy
from vnpy_ctastrategy.base import BacktestingMode
from vnpy.trader.constant import Interval, Exchange
from vnpy.trader.database import get_database

def test_with_real_data():
    """使用真实数据测试策略"""
    print("开始使用真实数据测试AlphaStrategy...")
    
    # 获取数据库实例
    database = get_database()
    
    # 检查可用的数据
    print("检查数据库中的可用数据...")
    overviews = database.get_bar_overview()
    
    if not overviews:
        print("数据库中没有找到任何数据！")
        print("请先通过VeighNa Studio的数据管理模块下载数据")
        return
    
    # 选择一个有足够数据的合约进行测试
    suitable_contract = None
    for overview in overviews:
        if overview.count > 100 and overview.interval == Interval.DAILY:  # 至少100条日线数据
            suitable_contract = overview
            break
    
    if not suitable_contract:
        # 如果没有日线数据，尝试分钟线数据
        for overview in overviews:
            if overview.count > 1000 and overview.interval == Interval.MINUTE:  # 至少1000条分钟线数据
                suitable_contract = overview
                break
    
    if not suitable_contract:
        print("没有找到合适的测试数据（需要至少100条日线或1000条分钟线数据）")
        print("可用的数据:")
        for overview in overviews[:10]:
            print(f"  {overview.symbol}.{overview.exchange.value}: {overview.interval.value} "
                  f"{overview.count} 条数据 ({overview.start} 到 {overview.end})")
        return
    
    print(f"选择测试合约: {suitable_contract.symbol}.{suitable_contract.exchange.value}")
    print(f"数据类型: {suitable_contract.interval.value}")
    print(f"数据量: {suitable_contract.count} 条")
    print(f"数据范围: {suitable_contract.start} 到 {suitable_contract.end}")
    
    # 创建回测引擎
    engine = BacktestingEngine()
    
    # 构建vt_symbol
    vt_symbol = f"{suitable_contract.symbol}.{suitable_contract.exchange.value}"
    
    # 设置回测参数
    # 使用更长的时间范围确保有足够数据
    end_date = suitable_contract.end
    if suitable_contract.interval == Interval.DAILY:
        # 使用所有可用数据，但至少保证有60天的数据
        start_date = max(suitable_contract.start, end_date - timedelta(days=120))
        interval = Interval.DAILY
    else:
        start_date = max(suitable_contract.start, end_date - timedelta(days=60))
        interval = suitable_contract.interval
    
    engine.set_parameters(
        vt_symbol=vt_symbol,
        interval=interval,
        start=start_date,
        end=end_date,
        rate=0.0002,  # 手续费
        slippage=0.001,  # 滑点
        size=1,  # 合约乘数
        pricetick=0.01,  # 最小价格变动
        capital=100000,  # 初始资金
        mode=BacktestingMode.BAR
    )
    
    # 策略参数
    strategy_setting = {
        "alpha_factors": ["alpha001", "alpha015", "alpha030"],
        "factor_weights": [0.33, 0.33, 0.34],
        "signal_threshold": 0.02,  # 降低阈值以增加交易机会
        "lookback_days": 10,  # 减少回看天数以适应有限的数据
        "debug_mode": True,
        "debug_interval": 5,  # 增加调试输出频率
        "risk_percent": 0.02,
        "max_position": 5
    }
    
    # 添加策略
    engine.add_strategy(AlphaStrategy, strategy_setting)
    
    print(f"开始回测 {vt_symbol} 从 {start_date} 到 {end_date}...")
    
    try:
        # 运行回测
        engine.run_backtesting()
        
        # 计算结果
        df = engine.calculate_result()
        
        # 输出结果
        print("\n" + "="*60)
        print("回测结果:")
        print("="*60)
        
        if engine.trades:
            print(f"总交易次数: {len(engine.trades)}")
            print("\n交易记录:")
            for i, (trade_id, trade) in enumerate(list(engine.trades.items())[:10]):
                direction = "买入" if trade.direction.value == "多" else "卖出"
                print(f"  {i+1}. {trade.datetime.strftime('%Y-%m-%d %H:%M')} {direction} {trade.volume}手 @ {trade.price:.2f}")
            
            if len(engine.trades) > 10:
                print(f"  ... 还有{len(engine.trades)-10}笔交易")
        else:
            print("没有产生交易记录")
        
        # 输出策略关键日志
        print(f"\n策略关键日志 (最后20条):")
        signal_logs = [log for log in engine.logs if any(keyword in log for keyword in ['信号:', '开多仓', '开空仓', '平仓', 'Alpha值', '数据检查'])]
        for log in signal_logs[-20:]:
            print(f"  {log}")
            
        if df is not None and not df.empty:
            print(f"\n回测统计:")
            if 'net_pnl' in df.columns:
                final_pnl = df['net_pnl'].iloc[-1]
                print(f"最终盈亏: {final_pnl:.2f}")
            if 'total_return' in df.columns:
                print(f"总收益率: {df['total_return'].iloc[-1]:.2%}")
            if 'max_drawdown' in df.columns:
                print(f"最大回撤: {df['max_drawdown'].iloc[-1]:.2%}")
        
    except Exception as e:
        print(f"回测过程中出现错误: {e}")
        
        # 输出详细的策略日志
        print(f"\n详细策略日志 (最后30条):")
        for log in engine.logs[-30:]:
            print(f"  {log}")

if __name__ == "__main__":
    test_with_real_data()
