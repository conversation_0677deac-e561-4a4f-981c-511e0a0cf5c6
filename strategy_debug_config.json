{"调试配置说明": "用于诊断AlphaStrategy为什么没有交易信号的配置文件", "基础调试配置": {"alpha_factors": ["alpha001", "alpha002", "alpha003", "alpha004", "alpha005"], "factor_weights": [0.2, 0.2, 0.2, 0.2, 0.2], "signal_threshold": 0.05, "lookback_days": 30, "risk_percent": 0.02, "atr_period": 20, "atr_multiplier": 2.0, "price_offset": 0.002, "max_position": 5, "debug_mode": true, "debug_interval": 5, "force_trade_test": false}, "强制交易测试配置": {"alpha_factors": ["alpha001", "alpha002", "alpha003"], "factor_weights": [0.33, 0.33, 0.34], "signal_threshold": 0.01, "lookback_days": 20, "risk_percent": 0.02, "atr_period": 14, "atr_multiplier": 1.5, "price_offset": 0.001, "max_position": 3, "debug_mode": true, "debug_interval": 3, "force_trade_test": true}, "高敏感度配置": {"alpha_factors": ["alpha001", "alpha015", "alpha030"], "factor_weights": [0.4, 0.3, 0.3], "signal_threshold": 0.01, "lookback_days": 25, "risk_percent": 0.015, "atr_period": 15, "atr_multiplier": 1.8, "price_offset": 0.0015, "max_position": 4, "debug_mode": true, "debug_interval": 8, "force_trade_test": false}, "调试步骤说明": ["1. 首先使用'基础调试配置'运行回测，查看调试日志", "2. 如果没有因子计算，检查历史数据是否充足", "3. 如果因子计算正常但没有信号，尝试降低signal_threshold", "4. 如果信号正常但没有交易，检查ATR和仓位计算", "5. 如果仍无交易，启用'强制交易测试配置'验证交易功能", "6. 确认交易功能正常后，使用'高敏感度配置'进行实际策略测试"], "常见问题解决方案": {"没有因子计算": {"可能原因": ["历史数据不足", "数据格式错误", "factor_alpha101模块问题"], "解决方案": ["增加lookback_days", "检查数据质量", "验证因子模块导入"]}, "没有交易信号": {"可能原因": ["signal_threshold过高", "因子值过小", "因子标准化问题"], "解决方案": ["降低signal_threshold到0.01-0.05", "检查因子计算结果", "调整因子权重"]}, "没有交易执行": {"可能原因": ["ATR计算错误", "仓位计算问题", "价格偏移设置"], "解决方案": ["检查ATR周期设置", "调整risk_percent", "减小price_offset"]}, "交易频率过高": {"可能原因": ["signal_threshold过低", "因子噪音过大"], "解决方案": ["提高signal_threshold", "增加因子平滑处理", "调整debug_interval"]}}, "参数说明": {"alpha_factors": "选择的Alpha101因子列表，建议3-5个", "factor_weights": "各因子权重，必须与因子数量匹配且和为1", "signal_threshold": "信号阈值，越小越敏感，建议0.01-0.1", "lookback_days": "历史数据天数，建议20-50", "risk_percent": "风险比例，建议0.01-0.03", "atr_period": "ATR计算周期，建议14-20", "atr_multiplier": "ATR止损倍数，建议1.5-2.5", "price_offset": "价格偏移比例，建议0.001-0.005", "max_position": "最大持仓手数，建议3-10", "debug_mode": "是否开启调试模式，建议true", "debug_interval": "调试信息输出间隔，建议3-10", "force_trade_test": "强制交易测试，用于验证交易功能"}}